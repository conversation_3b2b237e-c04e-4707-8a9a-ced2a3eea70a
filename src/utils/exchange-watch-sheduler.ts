import * as ccxt from 'ccxt';
import { logger } from './pinno-logger';
import { HHmmssSSS } from './date-format';

const exchangeWatchHelpers = new Map<ccxt.Exchange, ExchangeWatchScheduler>();

export function getWatchHelper(exchange: ccxt.Exchange): ExchangeWatchScheduler {
  let result = exchangeWatchHelpers.get(exchange);
  if (!result) {
    result = buildWatchHelperForExchange(exchange);
    exchangeWatchHelpers.set(exchange, result);
  }

  return result;
}

export const maxIndividuaMonitoredlSymbolsPerExchange = new Map<string, number>([
  ['binance', 30],
  ['bybit', 30],
  ['coinbase', 30],
  ['okx', 30],
  ['bitget', 30],
  ['mexc', 30],
  ['gate', 100],
  ['htx', 30],
  ['kucoin', 30],
  ['kraken', 30],
  ['bitfinex', 30],
  ['bitmart', 30],
  ['exmo', 30]
]);

export const maxSymbolsPerSlot = new Map<string, number>([
  ['binance', 200],
  ['bybit', 10],
  ['coinbase', 10],
  ['okx', 400],
  ['bitget', 100],
  ['mexc', 100],
  ['gate', 200],
  ['htx', 100],
  ['kucoin', 100],
  ['kraken', 50],
  ['bitfinex', 100],
  ['bitmart', 100],
  ['exmo', 10]
]);

function buildWatchHelperForExchange(exchange: ccxt.Exchange): ExchangeWatchScheduler {

  let maxSymbolsPerSlot = 10;
  let stepDelay = 5000;

  if (exchange.id === 'gate') {
    stepDelay = 1000;
    maxSymbolsPerSlot = 1;
  }

  //   │Exchange   │ Vulnerable│ Notes                                                                                    │
  //   ├───────────┼───────────┼──────────────────────────────────────────────────────────────────────────────────────────┤
  //   │ Binance   │ No        │ Handles multi-symbol subscriptions with a hardcoded limit of 200.                        │
  //   │ Bybit     │ Yes       │ API limit is 10 symbols per request.                                                     │
  //   │ Coinbase  │ Yes       │ API limit is 10 symbols per request.                                                     │
  //   │ OKX       │ Yes       │ API limit is 400 symbols per request.                                                    │
  //   │ Bitget    │ Yes       │ API limit is 100 symbols per request.                                                    │
  //   │ MEXC      │ Partially │ Swap markets are vulnerable, spot markets are not.                                       │
  //   │ Gate.io   │ No        │ Current implementation does not use batching for order books.                            │
  //   │ HTX       │ No        │ Protocol uses single-topic subscriptions that cannot be merged.                          │
  //   │ KuCoin    │ Yes       │ API limit is 100 symbols per request.                                                    │
  //   │ Kraken    │ Yes       │ API limit is 50 symbols per request.                                                     │
  //   │ Bitfinex  │ No        │ Protocol uses single-topic subscriptions that cannot be merged.                          │
  //   │ BitMart   │ Yes       │ Has a client-side limit of 20, but the API limit is 100, making it vulnerable to larg... │
  //   │ EXMO      │ Yes       │ No multi-symbol method, but vulnerable if called concurrently. API limit is 10 symbols   │
  return new ExchangeWatchScheduler(exchange, maxSymbolsPerSlot, stepDelay);
}

class ExchangeWatchScheduler {
  readonly exchange: ccxt.Exchange;
  readonly stepDelay: number;
  readonly maxSymbolsPerStep: number;

  constructor(exchange: ccxt.Exchange, maxSymbolsPerSlot = 10, stepDelay = 5000) {
    this.exchange = exchange;
    this.maxSymbolsPerStep = maxSymbolsPerSlot;
    this.stepDelay = stepDelay;
  }

  private startTime = Date.now();

  // 500ms step │ queue of 10 symbols
  // 1          │ ethusdt, btcusdt
  // 2          │ [adausdt, dogeusdt, solusdt, arbusdt, dotusdt, trxusdt, avaxusdt, shibusdt]
  // 3          │ ltcusdt, linkusdt, [uniusdt, filusdt, icpusdt]
  private scheduledSlots = Array<Array<string | string[]>>();

  public getDelayBeforeWatchSymbols(symbols: string | string[]): number {
    //logger.info(`[SCHEDULER] ${symbols} - Before cleanup: slots=${this.scheduledSlots.length}, startTime=${this.startTime}`);
    this.removeOldEntries();

    //logger.info(`[SCHEDULER] ${symbols} - After cleanup: slots=${this.scheduledSlots.length}, startTime=${this.startTime}`);
    if (this.scheduledSlots.length === 0) {
      this.startTime = Date.now();
      //logger.info(`[SCHEDULER] ${symbols} - Reset startTime to ${this.startTime}`);
    }

    const slotIndex = this.findFirstAvailableSlot(symbols);
    if (slotIndex >= 0) {
      this.scheduledSlots[slotIndex].push(symbols);
      const delay = this.getTimeToStartSlot(slotIndex);
      //logger.info(`[SCHEDULER] ${symbols} - Using existing slot ${slotIndex}, delay=${delay}ms`);
      return delay;
    }

    const newSlot = [];
    newSlot.push(symbols);
    this.scheduledSlots.push(newSlot);
    const finalSlotIndex = this.scheduledSlots.length - 1;
    const delay = this.getTimeToStartSlot(finalSlotIndex);
    //logger.info(`[SCHEDULER] ${symbols} - New slot ${finalSlotIndex}, delay=${delay}ms`);
    return delay;
  }

  private removeOldEntries() {
    const totalSteps = this.scheduledSlots.length;
    const stepsPassed = Math.floor((Date.now() - this.startTime) / this.stepDelay);
    const stepsToRemove = Math.min(totalSteps, stepsPassed);
    //logger.info(`[SCHEDULER] removeOldEntries: totalSteps=${totalSteps}, stepsPassed=${stepsPassed}, stepsToRemove=${stepsToRemove}`);

    if (stepsToRemove > 0) {
      const removed = this.scheduledSlots.splice(0, stepsToRemove);
      this.startTime += stepsToRemove * this.stepDelay;
      //logger.info(`[SCHEDULER] Removed ${removed} slots, remaining: ${this.scheduledSlots.length}`);
    }
  }

  private findFirstAvailableSlot(symbols: string | string[]): number | undefined {
    const symbolsToAdd = Array.isArray(symbols) ? symbols.length : 1;

    for (let i = 0; i < this.scheduledSlots.length; i++) {
      let symbolsInSlot = 0;
      for (const symbol of this.scheduledSlots[i]) {
        symbolsInSlot += Array.isArray(symbol) ? symbol.length : 1;
      }

      if (symbolsInSlot + symbolsToAdd <= this.maxSymbolsPerStep) {
        return i;
      }
    }

    return undefined;
  }

  private getTimeToStartSlot(slotIndex: number): number {
    const scheduledTime = this.startTime + (slotIndex * this.stepDelay);
    return Math.max(0, scheduledTime - Date.now()); // time left to start the slot
  }

  public async printStartWatchingSymbols(symbols: string | string[]): Promise<void> {
    if (Array.isArray(symbols)) {
      for (let i = 0; i < symbols.length; i++) {
        logger.info(`'${symbols[i]}' ${i}/${symbols.length - 1} start watching`);
        await new Promise(resolve => setTimeout(resolve, this.stepDelay / symbols.length));
      }

    } else {
      logger.info(`'${symbols}' start watching`);
    }
  }
}

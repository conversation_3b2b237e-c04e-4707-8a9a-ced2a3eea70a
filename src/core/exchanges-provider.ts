import * as ccxt from 'ccxt';
import { logger } from '../utils/pinno-logger';
import { takeRandomItem } from '../my-tests/my-tests-helpers';

interface EnvConfigs {
  apiKey: string;
  secret: string;
  password?: string;
}

export class ExchangesProvider {
  private exchangeConfigs: Map<string, EnvConfigs> = new Map<string, EnvConfigs>();
  private initializedExchanges = new Map<string, ccxt.Exchange>();

  constructor(jsonConfigs: string, singleExchangeName?: string) {
    const objConfigs = JSON.parse(jsonConfigs);// ["exchangeName", EnvConfigs]

    if (singleExchangeName) {
      this.exchangeConfigs.set(singleExchangeName, objConfigs[singleExchangeName]);
    } else {
      for (const exchangeName in objConfigs) {
        this.exchangeConfigs.set(exchangeName, objConfigs[exchangeName]);
      }
    }
  }

  public async createAllExchanges(): Promise<void> {
    const promises: Promise<ccxt.Exchange>[] = [];
    for (const [exchangeName, envConfig] of this.exchangeConfigs) {
      promises.push(this.createExchange(exchangeName, envConfig));
    }

    const exchanges = await Promise.all(promises);
    for (const exchange of exchanges) {
      this.initializedExchanges.set(exchange.id, exchange);
    }
  }

  public async createExchange(exchangeName: string, envConfig?: EnvConfigs): Promise<ccxt.Exchange> {
    const exchangeClass = (ccxt.pro)[exchangeName];
    if (!exchangeClass) {
      throw new ccxt.NotSupported(`Exchange ${exchangeName} not supported`);
    }

    if (!envConfig) {
      envConfig = this.exchangeConfigs.get(exchangeName);
    }

    if (!envConfig) {
      throw new ccxt.NotSupported(`Missing configurations for ${exchangeName}`);
    }

    const userConfig = {
      envConfig
    };

    const exchange: ccxt.Exchange = new exchangeClass(userConfig);
    exchange.options['maxRetriesOnFailure'] = 0; // we don't need retry logic, because we'll retry it in the next loop cycle anyway (BaseExchangeMonitor)
    exchange.enableRateLimit = true;
    
    // Add Gate-specific options to handle nonce synchronization issues
    if (exchangeName === 'gate') {
      exchange.options['watchOrderBook'] = {
        'snapshotMaxRetries': 5,
        'snapshotDelay': 100,
      };
    }
    
    const markets = await exchange.loadMarkets();
    // exchange.verbose = true;
    return exchange;
  }

  public get(exchangeName: string): ccxt.Exchange {
    return this.initializedExchanges.get(exchangeName);
  }

  public async getOrCreate(exchangeName: string): Promise<ccxt.Exchange> {
    const getResult = this.get(exchangeName);
    if (getResult) {
      return getResult;
    }

    const exchange = await this.createExchange(exchangeName);
    this.initializedExchanges.set(exchange.id, exchange);
    return exchange;
  }
  
  public all(): Map<string, ccxt.Exchange> {
    return this.initializedExchanges;
  }
}

export const exchanges = new ExchangesProvider(process.env.EXCHANGES, process.env.SINGLE_EXCHANGE_NAME);
import { exchanges, ExchangesProvider } from '../core/exchanges-provider';
import { ResponseMetrics } from './load-test';
import { ExchangeHelper } from '../utils/exchange-helper';
import { LINQ } from '../utils/linq';

export const config = {
  // Base URL of your crypto API
  // baseUrl: 'http://*************:3001',
  baseUrl: 'http://localhost:3001',

  // Supported exchanges
  exchanges: [
    'binance',
    'bybit',
    'coinbase',
    'okx',
    'bitget',
    'mexc',
    'gate',
    'htx',
    'kucoin',
    'kraken',
    'bitfinex',
    'bitmart',
    'exmo'
  ],

  // Popular crypto to test with
  symbols: [
    'BTC',   // Bitcoin
    'ETH',   // Ethereum
    'BNB',   // BNB
    'SOL',   // Solana
    'XRP',   // XRP
    'DOGE',  // <PERSON><PERSON>oin
    'TON',   // Toncoin
    'ADA',   // Cardano
    'AVAX',  // Avalanche
    'SHIB',  // Shiba Inu
    'DOT',   // Polkadot
    'TRX',   // TRON
    'LINK',  // Chainlink
    'BCH',   // Bitcoin Cash
    'NEAR',  // NEAR Protocol
    'LTC',   // Litecoin
    'UNI',   // Uniswap
    'ICP',   // Internet Computer
    'ETC',   // Ethereum Classic
    'XLM',   // Stellar
    'IMX',   // Immutable
    'INJ',   // Injective
    'FIL',   // Filecoin
    'ATOM',  // Cosmos
    'TIA',   // Celestia
    'ARB',   // Arbitrum
    'GRT',   // The Graph
    'OP',    // Optimism
    'PEPE',  // Pepe
    'THETA', // Theta Network
    'SUI',   // Sui
    'AAVE',  // Aave
    'SEI',   // Sei
    'FLOKI', // FLOKI
    'JUP',   // Jupiter
    'ALGO',  // Algorand
    'ONDO',  // Ondo
    'PENDLE',// Pendle
    'XTZ',   // Tezos
    'AXS',   // Axie Infinity
    'ROSE',  // Oasis Network
    'SAND',  // The Sandbox
    'FLOW',  // Flow
    'NEO',   // Neo
    'MINA',  // Mina
    'ZEC',   // Zcash
    'CHZ',   // Chiliz
    'IOTA',  // IOTA
    'STRK',  // Starknet
    'DYDX',  // dYdX
    'GMT',   // STEPN
    'COMP',  // Compound
    'BTT',   // BitTorrent
    'CVX'   // Convex Finance
  ],

  stables: ['USDT', 'USDC'],

  // Amount ranges to test for symbol1 (typically BTC/ETH/DOGE)
  symbolAmounts: [1.1, 2, 5.3, 10],
  // Amount ranges for symbol2 (typically USDT/USDC)
  stableAmounts: [100.23, 500.55, 1000, 1500.78, 2000.9, 5000],

  // Request timeouts
  timeout: '30s'
};

export function generateURLsAll(onlySupported: boolean = false): string[] {
  return [
    ...generateURLsSimple(onlySupported),
    ...generateURLsStableVolumes(onlySupported),
    ...generateURLsSymbolVolumes(onlySupported)
  ];
}

export function generateURLsSimple(onlySupported: boolean = false): string[] {
  const result = [];

  for (const exchangeName of config.exchanges) {
    const exchange = exchanges.get(exchangeName);
    if (onlySupported && !exchange) {
      continue;
    }
    for (const symbol of config.symbols) {
      for (const stable of config.stables) {
        if (onlySupported && !ExchangeHelper.findSupportedSymbols(exchange, symbol, stable)) {
          continue;
        }
        result.push(buildURL(config.baseUrl, exchangeName, symbol, stable));
      }
    }
  }

  return result;
}

export function generateURLsStableVolumes(onlySupported: boolean = false): string[] {
  const result = [];

  for (const exchangeName of config.exchanges) {
    const exchange = exchanges.get(exchangeName);
    if (onlySupported && !exchange) {
      continue;
    }
    for (const symbol of config.symbols) {
      for (const stable of config.stables) {
        if (!ExchangeHelper.findSupportedSymbols(exchange, symbol, stable)) {
          continue;
        }

        for (const amount of config.stableAmounts) {
          result.push(buildURL(config.baseUrl, exchangeName, symbol, stable, amount, stable));
        }
      }
    }
  }

  return result;
}

export function generateURLsSymbolVolumes(onlySupported: boolean = false): string[] {
  const result = [];

  for (const exchangeName of config.exchanges) {
    const exchange = exchanges.get(exchangeName);
    if (onlySupported && !exchange) {
      continue;
    }
    for (const symbol of config.symbols) {
      for (const stable of config.stables) {
        if (!ExchangeHelper.findSupportedSymbols(exchange, symbol, stable)) {
          continue;
        }
        for (const amount of config.symbolAmounts) {
          result.push(buildURL(config.baseUrl, exchangeName, symbol, stable, amount, symbol));
        }
      }
    }
  }

  return result;
}

export function buildURLsSimple(base: string, exchangeName: string, symbols:{symbol1:string, symbol2:string}[]): string[] {
  if(!base) {
    base = config.baseUrl;
  }
  
  const result = [];
  for (const symbol of symbols) {
    result.push(buildURL(base, exchangeName, symbol.symbol1, symbol.symbol2));
  }
  return result;
}

export function buildURLsStableVolumes(base: string, exchangeName: string, symbols:{symbol1:string, symbol2:string}[]): string[] {
  if(!base) {
    base = config.baseUrl;
  }
    
  const result = [];
  for (const symbol of symbols) {
    for (const amount of config.stableAmounts) {
      result.push(buildURL(base, exchangeName, symbol.symbol1, symbol.symbol2, amount, symbol.symbol2));
    }
  }
  return result;
}

export function buildURLsSymbolVolumes(base: string, exchangeName: string, symbols:{symbol1:string, symbol2:string}[]): string[] {
  if(!base) {
    base = config.baseUrl;
  }
  
  const result = [];
  for (const symbol of symbols) {
    for (const amount of config.stableAmounts) {
      result.push(buildURL(base, exchangeName, symbol.symbol1, symbol.symbol2, amount, symbol.symbol1));
    }
  }
  return result;
}

export function buildURL(base: string, exchange: string, symbol: string, stable: string, amount?: number | undefined, volumeSymbol?: string | undefined): string {
  if (amount && volumeSymbol) {
    return `${base}/${exchange}/${symbol}:${stable}:${amount}:${volumeSymbol}`;
  }

  return `${base}/${exchange}/${symbol}:${stable}`;
}



export function takeFirstItems<T>(array: T[], count: number): T[] {
  return array.splice(0, count);
}

export function takeLastItems<T>(array: T[], count: number): T[] {
  return array.splice(array.length - count, count);
}

export function takeRandomItem<T>(array: T[]): T {
  const randomIndex = Math.floor(Math.random() * array.length);
  const item = array[randomIndex];
  array.splice(randomIndex, 1);
  return item;
}

export function takeRandomItems<T>(array: T[], count: number): T[] {
  if(count >= array.length) {
    return array;
  }
  
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push(takeRandomItem(array));
  }
  return result;
}

export async function updateUrl(url: string, timeout = 30_000): Promise<ResponseMetrics> {
  const timeStart = Date.now();

  try {
    const response = await fetch(url, { signal: AbortSignal.timeout(timeout) });
    const text = await response.text();
    const executionTime = Date.now() - timeStart;
    
    const timeoutDiff = Math.abs(timeout - executionTime);
    
    return new ResponseMetrics(timeStart, response.status, Date.now() - timeStart, response.statusText);
  } catch (error) {
    if (error.name === 'TimeoutError') {
      return new ResponseMetrics(timeStart, 499, Date.now() - timeStart, 'cancelled:timeout');
    }

    return new ResponseMetrics(timeStart, 404, Date.now() - timeStart, error.name ?? error.toString());
  }
}

export function analyze(metrics: ResponseMetrics[]): {
  count: number;
  success: number;
  min: number;
  max: number;
  avg: number;
  p95: number;
  p95Avg: number;
} {
  if (metrics.length === 0) {
    return { count: 0, success: 0, min: 0, max: 0, avg: 0, p95: 0, p95Avg: 0 };
  }

  const count = metrics.length;
  const success = LINQ.count(metrics, m => m.statusCode === 200);

  const { min, max, avg } = LINQ.minMaxAvg(metrics, m => m.executionTime, true);

  const sorted = metrics.sort((a, b) => a.executionTime - b.executionTime);
  const p95Index = Math.floor(count * 0.95);
  const p95 = sorted[p95Index].executionTime;

  const early = LINQ.take(sorted, p95Index + 1);
  const p95Avg = LINQ.avg(early, m => m.executionTime, true);

  return { count, success, min, max, avg, p95, p95Avg };
}

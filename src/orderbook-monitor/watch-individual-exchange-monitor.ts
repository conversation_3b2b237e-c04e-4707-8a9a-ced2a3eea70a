import * as ccxt from 'ccxt';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { ExchangeHelper } from '../utils/exchange-helper';
import { ExchangeClosedByUser } from 'ccxt';
import { getConnectionsHelper } from '../utils/exchange-connections-helpers';
import { LINQ } from '../utils/linq';
import WsClient from 'ccxt/js/src/base/ws/WsClient';
import { getWatchHelper } from '../utils/exchange-watch-sheduler';
import { MonitoredSymbolsRegistry } from '../utils/monitored-symbols-registry';
import { exchanges } from '../core/exchanges-provider';
import { HHmmssSSS } from '../utils/date-format';

export class WatchIndividualExchangeMonitor extends BaseOrderbookMonitor {
  private static idCounter = 1;

  id: number = WatchIndividualExchangeMonitor.idCounter++;
  runningLoops = new Map<string, boolean>();
  onMonitoringFinish: () => void;

  async initialize(): Promise<void> {
    await super.initialize();
    if (this.exchangeName === 'mexc') {
      // coinbase only allows 30 symbols per connection and only 1 connection.
      // so we need to create a new exchange instance for each group.
      this.exchange = await exchanges.createExchange(this.exchangeName);
    }

    getConnectionsHelper(this.exchange).onClosingConnectionForSymbols.push((toCloseSubscriptions: string[][]) => {
      for (const subscriptions of toCloseSubscriptions) {
        if (subscriptions.length > 2) { // check only connection with no more than 2 symbols
          continue;
        }
        if (LINQ.any(subscriptions, (symbol) => this.hasSubscribers(symbol))) {
          logger.warn(`[${subscriptions.join(', ')}] has subscribers in individual monitor`);
          return false; // veto
        }
      }

      // logger.info(`Closing connection is safe for: [${symbols.join(', ')}]`);
      return true; // allow
    });
  }

  override subscribe(item: MonitoredRequest): boolean {
    const result = super.subscribe(item);
    if (!result) {
      return false;
    }

    if (!this.runningLoops.get(item.symbols)) {
      this.runningLoops.set(item.symbols, true);
      this.loop(item.symbols)
        .catch((error) => logger.error(error, 'WatchIndividualExchangeMonitor symbolLoop error:'))
        .finally(() => {
          this.runningLoops.delete(item.symbols);
          if (this.runningLoops.size === 0 && this.onMonitoringFinish != undefined) {
            this.onMonitoringFinish();
          }
        });
    }

    return result;
  }

  private loop(symbol: string): Promise<void> {
    if (this.exchange.has['unWatchOrderBook']) {
      return this.normalLoop(symbol);
    }

    if (ExchangeHelper.exchangeUsesMultipleClients(this.exchange)) {
      return this.closeConnectionLoop(symbol);
    }

    return this.forceUnwatchLoop(symbol);
  }

  private async normalLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    while (this.hasSubscribers(symbol)) {
      const orderbook = await this.watchSymbol(symbol);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    await this.unwatchSymbol(symbol);
    this.removeSymbolWithoutSubscribers(symbol);
  }

  private async closeConnectionLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    const beforeEstablishedConnectionClients = LINQ.toArray(Object.values(this.exchange.clients));

    // first update
    let orderbook: ccxt.OrderBook | Error = new Error();
    while (orderbook instanceof Error) {
      orderbook = await this.watchSymbol(symbol);
    }

    const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
    let client = connectionHelper.findClientServingSymbols(symbols, beforeEstablishedConnectionClients);
    if (client) {
      connectionHelper.stopClosingConnection(client, symbols);
    } else {
      throw new Error(`WatchIndividualExchangeMonitor failed to find a client responsible for '${symbol}'`);
    }

    while (this.hasSubscribers(symbol)) {
      const orderbook = await this.watchSymbol(symbol);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
    await this.closeConnectionForSymbol(client, symbol);
  }

  private async forceUnwatchLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    // avoid race condition when >10 symbols are added at the same time on bybit and maybe other exchanges
    const delay = getWatchHelper(this.exchange).getDelayBeforeWatchSymbols(symbol);
    if (delay > 0) {
      const scheduledTime = new Date(Date.now() + delay);
      logger.info(`'${symbol}' is scheduled for [${HHmmssSSS(scheduledTime)}] (delay: ${delay}ms)`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    getWatchHelper(this.exchange).printStartWatchingSymbols(symbol).catch((error) => logger.error(error, 'printWatchedSymbols error:'));

    while (this.hasSubscribers(symbol)) {
      const orderbook = await this.watchSymbol(symbol);
    }

    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
    if (!MonitoredSymbolsRegistry.isMonitored(this.exchange, symbol)) {
      await this.unwatchSymbol(symbol);
    }
    this.removeSymbolWithoutSubscribers(symbol);
  }

  private async watchSymbol(symbol: string): Promise<ccxt.OrderBook | Error> {
    try {
      const orderbook = await this.exchange.watchOrderBook(symbol);
      this.notifyUpdateSymbol(symbol, orderbook);
      return orderbook;
    } catch (error) {
      // if (error instanceof ExchangeClosedByUser) {
      //   // the underlying connection was closed, we should not watch these symbols anymore
      //   logger.warn(`WatchIndividualExchangeMonitor (${symbol}) connection closed, but we're still watching it`);
      //   return error;
      // }

      if (error instanceof ccxt.ChecksumError) {
        // ChecksumError is a typical error, no need to log it. 
        // Usually it signalizes that server has high trading activity at the moment, 
        // and our app cannot keep up with updates during subscription phase, 
        // or node server is overloaded and cannot process updates fast enough.
        // Either way, our retry logic will handle it.
        logger.warn(`WatchIndividualExchangeMonitor 'watchSymbol' ${symbol} ChecksumError.`);
      } else {
        logger.error(error, `WatchIndividualExchangeMonitor 'watchSymbol' ${symbol} error:`);
      }

      this.notifyUpdateSymbol(symbol, error);

      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return error;
    }
  }

  private async unwatchSymbol(symbol: string): Promise<boolean> {
    try {
      const result = await this.exchange.unWatchOrderBook(symbol);
      if (typeof result === 'boolean' && !result) {
        logger.warn(`WatchIndividualExchangeMonitor. Failed to unwatch '${symbol}'`);
        return false;
      }

      logger.info(`WatchIndividualExchangeMonitor. Unwatched '${symbol}'`);

      // only mark it as unwatched when it's not monitored.
      // unwatch failing is really an edge case.
      this.removeSymbolWithoutSubscribers(symbol);
      return true;
    } catch (error) {
      logger.error(error, `WatchIndividualExchangeMonitor 'unwatchSymbol' '${symbol}' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async closeConnectionForSymbol(client: WsClient, symbol: string): Promise<boolean> {
    // mark it as unwatched, because either way the corresponding 
    // client (ws connection) will be closed now or later.
    // closing client fails in case it is shared between several symbols.
    this.removeSymbolWithoutSubscribers(symbol);

    try {
      const symbols = [symbol];
      return await ExchangeHelper.getConnectionsHelper(this.exchange).tryToCloseConnection(client, symbols);
    } catch (error) {
      logger.error(error, `WatchIndividualExchangeMonitor 'closeConnectionForSymbol' ${symbol} error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }
}

import * as ccxt from 'ccxt';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { WatchGroupOrderbookMonitor } from './watch-group-orderbook-monitor';
import { WatchIndividualExchangeMonitor } from './watch-individual-exchange-monitor';
import { FetchIndividualOrderbookMonitor } from './fetch-individual-orderbook-monitor';
import * as os from 'node:os';
import { LINQ } from '../utils/linq';
import { ExchangeHelper } from '../utils/exchange-helper';
import { maxIndividuaMonitoredlSymbolsPerExchange, maxSymbolsPerSlot } from '../utils/exchange-watch-sheduler';

export class BalancerOrderbookMonitor extends BaseOrderbookMonitor {
  groupMonitors: WatchGroupOrderbookMonitor[];
  individualMonitors: WatchIndividualExchangeMonitor[];
  fetchMonitor: FetchIndividualOrderbookMonitor;

  // how many connection should serve the exchange's static symbols (TOP_TICKERS_NUMBER)
  private static CONNECTIONS_PER_EXCHANGE_STATIC_SYMBOLS = new Map<string, number>([
    ['binance', 6],
    ['gate', 3],
    ['mexc', 3],
    ['bitget', 3],
    ['bybit', 3],
    ['okx', 3],
    ['htx', 2],
    ['coinbase', 2],
    ['kucoin', 2],
    ['kraken', 2]
  ]);

  // used to create lists for TOP_TICKERS_NUMBER (by trading volume) symbols on each exchange 
  private static TOP_TICKERS_NUMBER = 60;

  // how many symbols needed to start monitoring existing group
  private MIN_SYMBOLS_TO_START_GROUP_MONITOR = 3;

  // how many symbols needed to create a new group
  private MIN_SYMBOLS_TO_CREATE_NEW_GROUP = 10;

  constructor(exchangeName: string) {
    super(exchangeName);
  }

  override toString(): string {
    let dynamic = 'individualMonitors (none)';
    if (this.individualMonitors) {
      const runningMonitors = LINQ.count(this.individualMonitors, (monitor) => LINQ.any(monitor.getMonitoredSymbols()));
      if (runningMonitors === 0) {
        dynamic = `0/${this.individualMonitors.length} individualMonitors (none)`;
      } else {
        dynamic = `${runningMonitors}/${this.individualMonitors.length} individualMonitors`;

        for (const monitor of this.individualMonitors) {
          if (LINQ.any(monitor.getMonitoredSymbols())) {
            const symbols = LINQ.count(monitor.getMonitoredSymbols());
            dynamic += ` '${monitor.id}':(${symbols})`;
          }
        }
      }
    }

    let fetch = 'fetchWatcher (none)';
    if (this.fetchMonitor) {
      const symbols = LINQ.toArray(this.fetchMonitor.getMonitoredSymbols());
      fetch = `fetchWatcher (${symbols.length}): ${symbols.join(', ')}`;
      // fetch = `fetchWatcher (${symbols.length})}`;
    }

    let groupStats = 'groupMonitors (none)';
    if (this.groupMonitors) {
      const runningMonitors = LINQ.count(this.groupMonitors, (monitor) => monitor.isRunning);
      if (runningMonitors === 0) {
        groupStats = `0/${this.groupMonitors.length} groupMonitors (none)`;
      } else {
        groupStats = `${runningMonitors}/${this.groupMonitors.length} groupMonitors`;

        for (const monitor of this.groupMonitors) {
          if (monitor.isRunning) {
            const symbols = LINQ.count(monitor.getMonitoredSymbols());
            groupStats += ` '${monitor.id}':(${symbols})`;
          }
        }
      }
    }

    return `${os.EOL}${dynamic}${os.EOL}${fetch}${os.EOL}${groupStats}`;
  }

  public override async initialize(): Promise<void> {
    await super.initialize();

    if (this.exchange.has['watchOrderBookForSymbols']) {
      this.groupMonitors = [];

      const topTickers = await ExchangeHelper.getTopTickersByVolume(this.exchange, BalancerOrderbookMonitor.TOP_TICKERS_NUMBER);
      const watchersToCreate = BalancerOrderbookMonitor.CONNECTIONS_PER_EXCHANGE_STATIC_SYMBOLS.get(this.exchange.id) || 2;
      const maxSymbolsPerGroup = maxSymbolsPerSlot.get(this.exchange.id);
      
      for (const groupedTickers of ExchangeHelper.groupSymbolsByActivity(topTickers, watchersToCreate, maxSymbolsPerGroup)) {
        const symbols = groupedTickers.symbols.map(ticket => ticket.symbol);
        await this.addNewGroupMonitor(symbols);
      }
    }

    //if (this.exchange.has['watchOrderBook'] && this.exchange.has['unWatchOrderBook']) {
    if (this.exchange.id !== 'coinbase') {
      if (this.exchange.has['watchOrderBook']
        && (this.exchange.has['unWatchOrderBook'] || ExchangeHelper.canForceUnWatchOrderBook(this.exchange))
        || ExchangeHelper.exchangeUsesMultipleClients) {
        this.individualMonitors = [];
        await this.addNewIndividualMonitor();
      }
    }
        
    // if (this.exchange.has['fetchOrderBooks']) {
    //   monitors.push(new FetchGroupOrderbookMonitor(this.exchange));
    // }

    if (this.exchange.has['fetchOrderBook']) {
      this.fetchMonitor = new FetchIndividualOrderbookMonitor(this.exchange.id);
      this.fetchMonitor.notifyUpdateSymbol = this.notifyUpdateSymbol;// to avoid missing updates when rebalancing
      await this.fetchMonitor.initialize();
    }

    // setInterval(()=>this.closeNonUsedExchanges().catch((error) =>
    //     logger.error(error, 'BalancerOrderbookMonitor closeNonUsedExchanges error:'))
    //   , 30_000);

    this.rebalanceLoop().catch((error) =>
      logger.error(error, 'BalancerOrderbookMonitor rebalanceLoop error:'));
  }


  // sometimes we get ws connection errors for unknown reasons 
  // (most likely, due to the bad connection or high exchange api load).
  // we need this method to clean up the failed connections and thus restore the exchange instance.
  async closeNonUsedExchanges(): Promise<void> {
    if (Object.keys(this.exchange.clients).length === 0) {
      return;
    }

    if (this.groupMonitors
      && LINQ.any(this.groupMonitors, (monitor) => monitor.isRunning)) {
      // do not close connections, if there are active group monitors
      return;
    }


    if (this.individualMonitors.length > 0
      && LINQ.any(this.individualMonitors[0].runningLoops.values(), (value) => value)) {
      // do not close connections, if there are any active individual monitoring loops
      return;
    }

    logger.info(`Closing '${this.exchange.id}' exchange due to no active monitoring`);
    await this.exchange.close();
  }

  private async rebalanceLoop(): Promise<void> {
    while (true) {
      await new Promise(resolve => setTimeout(resolve, 60_000));
      if (LINQ.count(this.fetchAndIndividualMonitorSymbols()) < this.MIN_SYMBOLS_TO_START_GROUP_MONITOR) {
        continue;
      }

      logger.info(`Rebalancing ${this.exchange.id}`);
      try {
        await this.rebalanceUp();
        await this.rebalanceDown();
      } catch (error) {
        logger.error(error, 'BalancerOrderbookMonitor rebalanceLoop error:');
      }
    }
  }

  private async addNewGroupMonitor(symbols: string[]): Promise<WatchGroupOrderbookMonitor> {
    const monitor = new WatchGroupOrderbookMonitor(this.exchange.id, symbols.sort());
    monitor.notifyUpdateSymbol = this.notifyUpdateSymbol;// to avoid missing updates when rebalancing
    await monitor.initialize();
    this.groupMonitors.push(monitor);
    return monitor;
  }

  private async addNewIndividualMonitor(): Promise<WatchIndividualExchangeMonitor> {
    const monitor = new WatchIndividualExchangeMonitor(this.exchange.id);
    monitor.notifyUpdateSymbol = this.notifyUpdateSymbol;// to avoid missing updates when rebalancing
    await monitor.initialize();
    this.individualMonitors.push(monitor);
    return monitor;
  }

  public override subscribe(item: MonitoredRequest): boolean {
    super.subscribe(item); // need this to be able to prioritize symbols when rebalancing

    // check if this symbol is already being monitored
    if (this.groupMonitors) {
      const monitoringMonitor = LINQ.first(this.groupMonitors,
        (monitor) => monitor.isMonitored(item.symbols));

      if (monitoringMonitor) {
        return monitoringMonitor.subscribe(item);
      }
    }

    if (this.individualMonitors) {
      const monitoringMonitor = LINQ.first(this.individualMonitors,
        (monitor) => monitor.isMonitored(item.symbols));

      if (monitoringMonitor) {
        return monitoringMonitor.subscribe(item);
      }
    }

    if (this.fetchMonitor && this.fetchMonitor.isMonitored(item.symbols)) {
      return this.fetchMonitor.subscribe(item);
    }

    // check if we can start monitoring this symbol
    if (this.individualMonitors) {
      const monitoringMonitor = LINQ.first(this.individualMonitors,
        (monitor) => LINQ.count(monitor.getMonitoredSymbols()) < maxIndividuaMonitoredlSymbolsPerExchange.get(monitor.exchangeName));

      if (monitoringMonitor) {
        return monitoringMonitor.subscribe(item);
      }
    }

    if (this.fetchMonitor) {
      return this.fetchMonitor.subscribe(item);
    }

    logger.error(`No monitor found for ${item.symbols} on ${this.exchange.id}`);
    return false;
  }

  public override unsubscribe(item: MonitoredRequest): boolean {
    super.unsubscribe(item); // need this to be able to prioritize symbols when rebalancing
    this.removeSymbolWithoutSubscribers(item.symbols);

    if (this.groupMonitors && LINQ.any(this.groupMonitors,
      (monitor) =>
        monitor.isRunning
        && LINQ.contains(monitor.symbols, item.symbols)
        && monitor.unsubscribe(item))) {
      return true;
    }

    if (this.individualMonitors && LINQ.any(this.individualMonitors,
      (monitor) =>
        monitor.isMonitored(item.symbols)
        && monitor.unsubscribe(item))) {
      return true;
    }

    if (this.fetchMonitor?.unsubscribe(item)) {
      return true;
    }

    logger.error(`No monitor found for ${item.symbols} on ${this.exchange.id}`);
    return false;
  }

  // overriden, because this.symbolSubscribers doesn't represent the actual monitored symbols.
  // we unsubscribe instantly, while the monitor might still be in the process of monitoring symbol.
  public override isMonitored(symbol: string): boolean {
    if (this.groupMonitors && LINQ.any(this.groupMonitors,
      (monitor) => monitor.isMonitored(symbol))) {
      return true;
    }

    if (this.individualMonitors && LINQ.any(this.individualMonitors,
      (monitor) => monitor.isMonitored(symbol))) {
      return true;
    }

    if (this.fetchMonitor?.isMonitored(symbol)) {
      return true;
    }

    return false;
  }

  public override* getMonitoredSymbols(): IterableIterator<string> {
    if (this.groupMonitors) {
      for (const monitor of this.groupMonitors) {
        for (const symbol of monitor.getMonitoredSymbols()) {
          yield symbol;
        }
      }
    }

    if (this.individualMonitors) {
      for (const monitor of this.individualMonitors) {
        for (const symbol of monitor.getMonitoredSymbols()) {
          yield symbol;
        }
      }
    }

    if (this.fetchMonitor) {
      for (const symbol of this.fetchMonitor.getMonitoredSymbols()) {
        yield symbol;
      }
    }
  }


  private* fetchAndIndividualMonitorSymbols(): IterableIterator<string> {
    if (this.fetchMonitor) {
      for (const symbol of this.fetchMonitor.getMonitoredSymbols()) {
        yield symbol;
      }
    }

    if (this.individualMonitors) {
      for (const monitor of this.individualMonitors) {
        for (const symbol of monitor.getMonitoredSymbols()) {
          yield symbol;
        }
      }
    }
  }

  private promoteToGroupMonitor(symbols: string[], monitor: WatchGroupOrderbookMonitor) {
    for (const symbol of symbols) {
      if (this.individualMonitors) {
        const individualMonitor = LINQ.first(this.individualMonitors,
          (monitor) => monitor.isMonitored(symbol));
        if (!individualMonitor || !individualMonitor.hasSubscribers(symbol)) {
          continue;
        }
        logger.info(`[${symbol}] MovesUP individualMonitor '${individualMonitor.id}' -> groupMonitor '${monitor.id}'`);
        this.subscribers.length = 0; // copy list, because we'll alter subscribers in the loop
        LINQ.toArray(individualMonitor.getSubscribers(symbol), this.subscribers);
        for (const subscriber of this.subscribers) {
          individualMonitor.unsubscribe(subscriber);
          monitor.subscribe(subscriber);
        }
      }

      if (this.fetchMonitor && this.fetchMonitor.hasSubscribers(symbol)) {
        logger.info(`[${symbol}] MovesUP fetchWatcher -> groupMonitor '${monitor.id}'`);
        this.subscribers.length = 0; // copy list, because we'll alter subscribers in the loop
        LINQ.toArray(this.fetchMonitor.getSubscribers(symbol), this.subscribers);
        for (const subscriber of this.subscribers) {
          this.fetchMonitor.unsubscribe(subscriber);
          monitor.subscribe(subscriber);
        }
      }
    }

  }

  private async rebalanceUp(): Promise<void> {
    await this.rebalanceUpToGroups();
    await this.rebalanceUpToIndividual();
  }

  private async rebalanceUpToGroups() {
    if (!this.groupMonitors) {
      return;
    }

    // first, try to upgrade to the existing group monitors
    let symbolsToPromote: string[] = [];
    for (const monitor of this.groupMonitors) {
      const iterator = LINQ.where(this.fetchAndIndividualMonitorSymbols(),
        (symbol) => monitor.symbols.includes(symbol));

      symbolsToPromote.length = 0;
      LINQ.toArray(iterator, symbolsToPromote);

      if (!monitor.isRunning && symbolsToPromote.length <= this.MIN_SYMBOLS_TO_START_GROUP_MONITOR) {
        // not enough symbols to justify monitoring entire group
        continue;
      }

      this.promoteToGroupMonitor(symbolsToPromote, monitor);
    }

    // exclude symbols that can be upgraded to the existing group monitors later
    const iterator = LINQ.where(this.fetchAndIndividualMonitorSymbols(),
      (symbol) => LINQ.all(this.groupMonitors,
        (monitor) => !monitor.symbols.includes(symbol)));
    const notUpgradedSymbols = LINQ.toArray(iterator);

    // const symbolAvgEmaRequestPeriod =
    //   this.getSymbolsAvgEmaRequestPeriod(notUpgradedSymbols);
    //
    // // order symbols by emaRequestPeriod
    // notUpgradedSymbols.sort((a, b) => {
    //   const aEma = symbolAvgEmaRequestPeriod.get(a);
    //   const bEma = symbolAvgEmaRequestPeriod.get(b);
    //   return aEma - bEma;
    // });

    // create new group monitors and upgrade remaining symbols to them
    if (notUpgradedSymbols.length > this.MIN_SYMBOLS_TO_CREATE_NEW_GROUP) {
      const maxSymbolsPerGroup = maxSymbolsPerSlot.get(this.exchange.id);
      const groups = this.sliceSymbolsIntoGroups(notUpgradedSymbols, this.MIN_SYMBOLS_TO_CREATE_NEW_GROUP, maxSymbolsPerGroup);
      for (const group of groups) {
        const newMonitor = await this.addNewGroupMonitor(group);
        newMonitor.onMonitoringFinish = () => this.removeGroupMonitor(newMonitor);
        logger.info(`Created new groupMonitor '${newMonitor.id}' for ${group.length} symbols`);
        this.promoteToGroupMonitor(group, newMonitor);
      }
    }
  }

  private async rebalanceUpToIndividual() {
    if (this.exchange.id !== 'mexc') {
      return;
    }
    
    if (this.exchange.id !== 'mexc' && this.exchange.id !== 'gate') {
      return;
    }

    if (!this.fetchMonitor || !LINQ.any(this.fetchMonitor.getMonitoredSymbols())) {
      return;
    }

    if (!this.individualMonitors) {
      return;
    }

    // upgrade symbols to existing individual monitors
    for (const individualMonitor of this.individualMonitors) {
      for (const symbol of this.fetchMonitor.getMonitoredSymbols()) {
        if (individualMonitor.isMonitored(symbol)) {
          this.promoteToIndividualMonitor(this.fetchMonitor, individualMonitor, symbol);
        }
      }
    }

    if (!LINQ.any(this.fetchMonitor.getMonitoredSymbols())) {
      return;
    }

    const iterator = LINQ.where(this.fetchMonitor.getMonitoredSymbols(),
      (symbol) => this.fetchMonitor.hasSubscribers(symbol));
    const notUpgradedSymbols = LINQ.toArray(iterator);
    if (notUpgradedSymbols.length === 0) {
      return;
    }

    const maxSymbolsPerIndividualMonitor = maxIndividuaMonitoredlSymbolsPerExchange.get(this.exchangeName);
    for (const individualMonitor of this.individualMonitors) {
      const monitoredSymbolsCount = LINQ.count(individualMonitor.getMonitoredSymbols());
      let remainingCapacity = maxSymbolsPerIndividualMonitor - monitoredSymbolsCount;
      while (notUpgradedSymbols.length > 0 && remainingCapacity > 0) {
        const symbol = notUpgradedSymbols.pop();
        this.promoteToIndividualMonitor(this.fetchMonitor, individualMonitor, symbol);
        remainingCapacity--;
      }
    }

    if (notUpgradedSymbols.length === 0) {
      return;
    }

    const groups = this.sliceSymbolsIntoGroups(notUpgradedSymbols, 1, maxSymbolsPerIndividualMonitor);
    for (const group of groups) {
      const newMonitor = await this.addNewIndividualMonitor();
      newMonitor.onMonitoringFinish = () => this.removeIndividualMonitor(newMonitor);
      logger.info(`Created new individualMonitor '${newMonitor.id}' for ${group.length} symbols`);
      for (const symbol of group) {
        this.promoteToIndividualMonitor(this.fetchMonitor, newMonitor, symbol);
      }
    }
  }

  private subscribers: MonitoredRequest[] = [];

  private promoteToIndividualMonitor(fetchMonitor: FetchIndividualOrderbookMonitor, individualMonitor: WatchIndividualExchangeMonitor, symbol: string) {
    logger.info(`[${symbol}] MovesUP fetchWatcher -> individualMonitor '${individualMonitor.id}'`);
    this.subscribers.length = 0; // copy list, because we'll alter subscribers later
    LINQ.toArray(fetchMonitor.getSubscribers(symbol), this.subscribers);
    for (const subscriber of this.subscribers) {
      fetchMonitor.unsubscribe(subscriber);
      individualMonitor.subscribe(subscriber);
    }
  }

  private sliceSymbolsIntoGroups(symbols: string[], minSymbolsPerGroup: number, maxSymbolsPerGroup?: number): string[][] {
    if(maxSymbolsPerGroup && minSymbolsPerGroup > maxSymbolsPerGroup){
      throw new Error(`minSymbolsPerGroup ${minSymbolsPerGroup} is greater than maxSymbolsPerGroup ${maxSymbolsPerGroup}`);
    }

    const total = symbols.length;

    // Guard: not enough symbols for one group
    if (total < minSymbolsPerGroup) {
      return [];
    }

    // Guard: not enough symbols for two groups, but enough for one
    if (maxSymbolsPerGroup && total < maxSymbolsPerGroup) {
      return [symbols];
    }

    const numGroups = maxSymbolsPerGroup ? Math.floor(total / maxSymbolsPerGroup) : 1;
    
    // Start with a base group size
    let groupSize = Math.floor(total / numGroups);
    if (groupSize < minSymbolsPerGroup) {
      groupSize = minSymbolsPerGroup;
    }

    const result: string[][] = [];
    let index = 0;
    for (let i = 0; i < numGroups; i++) {
      result.push(symbols.slice(index, index + groupSize));
      index += groupSize;
    }

    // If undistributed symbols remain here, they are ignored (not assigned to any group)
    return result;
  }

  private removeIndividualMonitor(monitor: WatchIndividualExchangeMonitor) {
    if (LINQ.any(monitor.getMonitoredSymbols())) {
      throw new Error(`You cannot remove this individual monitor (${monitor.id}). It's still running.`);
    }

    logger.info(`Removing not running individual monitor (${monitor.id})`);
    const index = this.individualMonitors.indexOf(monitor);
    if (index !== -1) {
      this.individualMonitors.splice(index, 1);
    }

    monitor.exchange.close();
  }

  private removeGroupMonitor(monitor: WatchGroupOrderbookMonitor) {
    if (monitor.isRunning) {
      throw new Error(`You cannot remove this group monitor (${monitor.id}). It's still running.`);
    }

    logger.info(`Removing not running group monitor (${monitor.id})`);
    const index = this.groupMonitors.indexOf(monitor);
    if (index !== -1) {
      this.groupMonitors.splice(index, 1);
    }
  }

  private getSymbolsAvgEmaRequestPeriod(symbols: string[]): Map<string, number> {
    const result = new Map<string, number>();
    for (const symbol of symbols
      ) {
      let total = 0;
      let count = 0;
      for (const subscriber of this.getSubscribers(symbol)) {
        total += subscriber.emaRequestPeriod;
        count++;
      }
      result.set(symbol, this.getAvgEmaRequestPeriod(symbol));
    }
    return result;
  }

  private getAvgEmaRequestPeriod(symbol: string): number {
    let total = 0;
    let count = 0;
    for (const subscriber of this.getSubscribers(symbol)) {
      total += subscriber.emaRequestPeriod;
      count++;
    }

    return Math.round(total / count);
  }

  private async rebalanceDown(): Promise<void> {
    if (!this.groupMonitors) {
      return;
    }

    let subscribers: MonitoredRequest[] = [];

    for (const monitor of this.groupMonitors) {
      if (!monitor.isRunning) {
        continue;
      }

      let subscribedSymbolsCount = 0;
      for (const symbol of monitor.getMonitoredSymbols()) {
        if (LINQ.any(monitor.getSubscribers(symbol))) {
          subscribedSymbolsCount++;
        }
      }

      if (subscribedSymbolsCount > this.MIN_SYMBOLS_TO_START_GROUP_MONITOR) {
        // there are still enough symbols to justify a multiplexed connection
        continue;
      }

      for (const symbol of monitor.getMonitoredSymbols()) {
        subscribers.length = 0; // copy list, because we'll alter subscribers later
        LINQ.toArray(monitor.getSubscribers(symbol), subscribers);

        if (subscribers.length === 0) {
          continue;
        }

        if (this.individualMonitors) {
          const individualMonitor = LINQ.min(this.individualMonitors,
            (monitor) => LINQ.count(monitor.getMonitoredSymbols()),
            (monitor) => monitor);

          let connectionsLeft = maxIndividuaMonitoredlSymbolsPerExchange.get(this.exchangeName) - (LINQ.count(individualMonitor.getMonitoredSymbols()));
          if (connectionsLeft > 0 && subscribers.length > 0) {
            logger.info(`[${symbol}] MovesDOWN groupMonitor '${monitor.id}' -> individualMonitor (${subscribers.length}/${connectionsLeft})`);
          }

          for (let i = subscribers.length - 1; i >= 0; i--) {
            if (connectionsLeft <= 0) {
              break;
            }

            const subscriber = subscribers[i];
            monitor.unsubscribe(subscriber);
            individualMonitor.subscribe(subscriber);
            subscribers.splice(i, 1);
            connectionsLeft--;
          }
        }

        if (this.fetchMonitor && subscribers.length > 0) {
          logger.info(`[${symbol}] MovesDOWN groupMonitor '${monitor.id}' -> fetchWatcher (${subscribers.length})`);
          for (let i = subscribers.length - 1; i >= 0; i--) {
            const subscriber = subscribers[i];
            monitor.unsubscribe(subscriber);
            this.fetchMonitor.subscribe(subscriber);
            subscribers.splice(i, 1);
          }
        }
      }
    }
  }
}

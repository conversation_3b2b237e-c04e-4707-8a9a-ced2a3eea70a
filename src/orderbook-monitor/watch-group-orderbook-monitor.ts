import * as ccxt from 'ccxt';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { LINQ } from '../utils/linq';
import { ExchangeHelper } from '../utils/exchange-helper';
import { ExchangeClosedByUser, ExchangeError } from 'ccxt';
import { getConnectionsHelper } from '../utils/exchange-connections-helpers';
import WsClient from 'ccxt/js/src/base/ws/WsClient';
import { getWatchHelper } from '../utils/exchange-watch-sheduler';
import { MonitoredSymbolsRegistry } from '../utils/monitored-symbols-registry';
import { exchanges } from '../core/exchanges-provider';
import { HHmmssSSS } from '../utils/date-format';

export class WatchGroupOrderbookMonitor extends BaseOrderbookMonitor {
  private static idCounter = 1;

  id: number = WatchGroupOrderbookMonitor.idCounter++;
  symbols: string[];
  isRunning = false;
  onMonitoringFinish: () => void;

  constructor(exchangeName: string, symbols: string[]) {
    super(exchangeName);
    this.symbols = symbols;
  }

  async initialize(): Promise<void> {
    await super.initialize();
    if (this.exchangeName === 'coinbase') {
      // coinbase only allows 30 symbols per connection and only 1 connection.
      // so we need to create a new exchange instance for each group.
      this.exchange = await exchanges.createExchange(this.exchangeName);
    }

    getConnectionsHelper(this.exchange).onClosingConnectionForSymbols.push((toCloseSubscriptions: string[][]) => {
        if (!LINQ.containsList(toCloseSubscriptions, this.symbols)) {
          return undefined; // not responsible
        }

        if (LINQ.any(this.symbols, (symbol) => this.hasSubscribers(symbol))) {
          logger.warn(`[${this.symbols.join(', ')}] has subscribers in groupMonitor '${this.id}'`);
          return false; // veto
        }

        // logger.info(`Closing connection is safe by groupMonitor '${this.id}' for: [${symbols.join(', ')}]`);
        return true; // allow
      }
    );
  }

  override subscribe(item: MonitoredRequest): boolean {
    if (!LINQ.contains(this.symbols, item.symbols)) {
      throw new Error(`'${item.symbols}' is not supported by this group (${this.id}).`);
    }

    // add the item to the subscribers list
    const result = super.subscribe(item);

    if (!this.isRunning) {
      this.isRunning = true;
      this.loop(this.symbols)
        .catch((error) => {
          logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'loop' error:`);
        })
        .finally(() => {
          this.isRunning = false;
          if (this.onMonitoringFinish != undefined) {
            this.onMonitoringFinish();
          }
        });
    }

    return result;
  }

  override unsubscribe(item: MonitoredRequest): boolean {
    if (!LINQ.contains(this.symbols, item.symbols)) {
      throw new Error(`'${item.symbols}' is not supported by this group (${this.id}).`);
    }

    // add the item to the subscribers list
    return super.unsubscribe(item);
  }

  private loop(symbols: string[]): Promise<void> {
    if (this.exchange.has['unWatchOrderBook']) {
      return this.normalLoop(symbols);
    }

    if (ExchangeHelper.exchangeUsesMultipleClients(this.exchange)) {
      return this.closeConnectionLoop(symbols);
    }
    
    if(this.exchangeName === 'coinbase') {
      return this.closeExchangeLoop(symbols);
    }

    return this.forceUnwatchLoop(symbols);
  }

  private async normalLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    while (this.hasSubscribers()) {
      const orderbook = await this.watchSymbols(symbols);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    await this.unwatchSymbols(symbols);
    for (const symbol of symbols) {
      this.removeSymbolWithoutSubscribers(symbol);
    }
  }

  private async closeConnectionLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    const beforeEstablishedConnectionClients = LINQ.toArray(Object.values(this.exchange.clients));

    // first update
    let orderbook: ccxt.OrderBook | Error = new Error();
    while (orderbook instanceof Error) {
      orderbook = await this.watchSymbols(symbols);
    }

    const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
    let client = connectionHelper.findClientServingSymbols(symbols, beforeEstablishedConnectionClients);
    if (client) {
      connectionHelper.stopClosingConnection(client, symbols);
    } else {
      throw new Error(`WatchGroupOrderbookMonitor '${this.id}' failed to find a client responsible for symbols`);
    }

    while (this.hasSubscribers()) {
      const orderbook = await this.watchSymbols(symbols);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    // mark as unwatched, because either way the corresponding 
    // client (ws connection) will be closed now or later.
    // closing client fails in case it is shared between several symbols.
    for (const symbol of symbols) {
      this.removeSymbolWithoutSubscribers(symbol);
    }

    await this.closeConnectionForSymbols(client, symbols);
  }

  private async forceUnwatchLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    // avoid race condition when >10 symbols are added at the same time on bybit and maybe other exchanges
    const delay = getWatchHelper(this.exchange).getDelayBeforeWatchSymbols(symbols);
    if (delay > 0) {
      const scheduledTime = new Date(Date.now() + delay);
      logger.info(`[${symbols.join(', ')}] is scheduled for [${HHmmssSSS(scheduledTime)}] (delay: ${delay}ms)`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    getWatchHelper(this.exchange).printStartWatchingSymbols(symbols).catch((error) => logger.error(error, 'printWatchedSymbols error:'));

    while (this.hasSubscribers()) {
      const orderbook = await this.watchSymbols(symbols);
    }

    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    for (const symbol of symbols) {
      if (!MonitoredSymbolsRegistry.isMonitored(this.exchange, symbol)) {
        await this.unwatchSymbol(symbol);
      }
      this.removeSymbolWithoutSubscribers(symbol);
    }
  }

  private async closeExchangeLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    while (this.hasSubscribers()) {
      const orderbook = await this.watchSymbols(symbols);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    // mark as unwatched, because either way the corresponding 
    // client (ws connection) will be closed now or later.
    // closing client fails in case it is shared between several symbols.
    for (const symbol of symbols) {
      this.removeSymbolWithoutSubscribers(symbol);
    }
    
    await this.exchange.close();
  }
  
  private async unwatchSymbol(symbol: string): Promise<boolean> {
    try {
      const result = await this.exchange.unWatchOrderBook(symbol);
      if (typeof result === 'boolean' && !result) {
        logger.warn(`WatchGroupOrderbookMonitor '${this.id}' Failed to unwatch '${symbol}'`);
        return false;
      }
      logger.info(`WatchGroupOrderbookMonitor '${this.id}'. Unwatched '${symbol}'`);
      return true;
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'unwatchSymbol' '${symbol}' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async watchSymbols(symbols: string[]): Promise<ccxt.OrderBook | Error> {
    try {
      const orderbook = await this.exchange.watchOrderBookForSymbols(symbols);
      this.notifyUpdateSymbol(orderbook.symbol, orderbook);
      return orderbook;
    } catch (error) {
      // if (error instanceof ExchangeClosedByUser) {
      //   // connection has closed, we should not watch these symbols anymore
      //   logger.warn(`WatchGroupOrderbookMonitor '${this.id}' connection closed, but we still watch it`);
      //   return error;
      // }

      const symbol = this.extractSymbolName(error);
      if (symbol) {
        if (error instanceof ccxt.ChecksumError) {
          // ChecksumError is a typical error, no need to log it. 
          // Usually it signalizes that server has high trading activity at the moment, 
          // and our app cannot keep up with updates during subscription phase, 
          // or node server is overloaded and cannot process updates fast enough.
          // Either way, our retry logic will handle it.
          logger.warn(`WatchGroupOrderbookMonitor '${this.id}' 'watchSymbols' '${symbol}' ChecksumError.`);
        } else {
          logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'watchSymbols' '${symbol}' error:`);
        }

        this.notifyUpdateSymbol(symbol, error);
      } else {
        logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'watchSymbols' error:`);
        for (const symbol of symbols) {
          this.notifyUpdateSymbol(symbol, error);
        }
      }

      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return error;
    }
  }

  private extractSymbolName(err: ExchangeError): string | undefined {
    // {
    //   "name": "ChecksumError",
    //   "message": "binance DOGE/USDT : orderbook data checksum validation failed. 
    //               You can reconnect by calling watchOrderBook again, or you can mute the error by 
    //               setting exchange.options[\"watchOrderBook\"][\"checksum\"] = false"
    // }
    // Example regex: captures something like "DOGE/USDT" after "binance "
    const match = err.message.match(/\b([A-Z0-9]+\/[A-Z0-9]+)\b/);
    return match?.[1];
  }

  private async unwatchSymbols(symbols: string[]): Promise<boolean> {
    logger.info(`WatchGroupOrderbookMonitor '${this.id}' unwatching symbols: ${symbols.join(', ')}`);
    try {
      const result = await this.exchange.unWatchOrderBookForSymbols(symbols);
      if (typeof result === 'boolean' && !result) {
        logger.warn(`WatchGroupOrderbookMonitor '${this.id}' Failed to unwatch symbols`);
        return false;
      }

      return true;
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'unwatchRequests' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async closeConnectionForSymbols(client: WsClient, symbols: string[]): Promise<boolean> {
    try {
      return await ExchangeHelper.getConnectionsHelper(this.exchange).tryToCloseConnection(client, symbols);
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'unwatchRequests' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }
}

{
  "compilerOptions": {
    "module": "commonjs",
    "target": "es2022",
    "outDir": "out" /* Change to "dist" or "lib" as needed. */,
    "sourceMap": true,
    "removeComments": false,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "lib": ["es2022", "dom"],
    /* Additional Checks */
    //"strict": true /* Enable all strict type checking options. */,
    // "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
    // "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
    // "noUnusedParameters": true,  /* Report errors on unused parameters. */

    // need to make navigation to ccxt source code possible 
    "baseUrl": ".",
    "paths": {
      "ccxt": ["./node_modules/ccxt", "./ccxt-source/ts/ccxt"],
      "ccxt/*": ["./node_modules/ccxt/*", "./ccxt-source/ts/src/*"]
    }
  },
  "include": ["src"],
  "exclude": ["ccxt-source", "out"]
}

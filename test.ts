import * as dotenv from 'dotenv';

dotenv.config();

import { logger } from './src/utils/pinno-logger';

import * as ccxt from 'ccxt';
import { exchanges } from './src/core/exchanges-provider';


async function watchAllOrderBooks(allSymbols: string[], chunkSize: number) {
  logger.info(`Starting to watch order books for ${allSymbols.length} symbols...`
  );

  // Split symbols into chunks of 10
  const chunks = new Map<ccxt.Exchange, string[]>();
  for (let i = 0; i < allSymbols.length; i += chunkSize) {
    logger.info(`Creating exchange ${i}`);
    const exchange: ccxt.Exchange = await exchanges.createExchange('coinbase');
    chunks.set(exchange, (allSymbols.slice(i, i + chunkSize)));
    logger.info(`Created exchange ${i}`);
  }

  const timeToStop = Date.now() + 60_000;
  while (Date.now() < timeToStop) {
    let i = 0;
    for (const [exchange, symbols] of chunks.entries()) {
      try {
        logger.info(`watching ${i}`);
        await exchange.watchOrderBookForSymbols(symbols);
      } catch (error) {
        logger.error(error, `Error watching ${i}:`);
      }
      i++;
    }
  }
}

async function main() {
  const chunkSize = 10;
  const allSymbols = [
    'BTC-USD', 'ETH-USD', 'LTC-USD', 'BCH-USD', 'ETC-USD', 'ZRX-USD', 'BAT-USD', 'LINK-USD', 'XLM-USD', 'EOS-USD',
    'XTZ-USD', 'OXT-USD', 'ATOM-USD', 'ALGO-USD', 'DASH-USD', 'FIL-USD', 'GRT-USD', 'MKR-USD', 'COMP-USD', 'AAVE-USD',
    'UNI-USD', 'SNX-USD', 'YFI-USD', 'KNC-USD', 'SOL-USD', 'UMA-USD', 'MLN-USD', 'SUI-USD', 'APT-USD', 'CRV-USD',
    'BAL-USD', 'SUSHI-USD', 'PERP-USD', 'DOGE-USD', 'LRC-USD', 'KAVA-USD', 'RSR-USD', 'NMR-USD', 'OCEAN-USD', 'STORJ-USD'
  ];

  await watchAllOrderBooks(allSymbols, chunkSize);
}

// This will now correctly subscribe to all 12 symbols over a single connection
// by sending two separate subscription requests.
main().catch(error => {
  console.error('Error watching symbols:', error);
});
import eslintPluginTypescript from "@typescript-eslint/eslint-plugin";
import eslintParserTypescript from "@typescript-eslint/parser";
import eslintPluginUnicorn from "eslint-plugin-unicorn";

export default [
    {
        files: ["**/*.ts", "**/*.tsx"],
        languageOptions: {
            parser: eslintParserTypescript,
        },
        plugins: {
            "@typescript-eslint": eslintPluginTypescript,
            "unicorn": eslintPluginUnicorn,
        },
        rules: {
            ...eslintPluginTypescript.configs.recommended.rules,
            ...eslintPluginUnicorn.configs.recommended.rules,
            // "unicorn/prefer-query-selector": "error", // Example of enforcing a Unicorn rule
            "@typescript-eslint/no-unused-vars": "off", // Disabling conflicting TypeScript rule
            "unicorn/prevent-abbreviations": "off", // Disabling conflicting TypeScript rule
            "unicorn/prefer-top-level-await": "off",
        },
    },
];


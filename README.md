# crypto-monitor

## CCXT Integration Setup

This project uses a **dual CCXT setup** for optimal development experience:
- **NPM Package** (`ccxt@^4.5.2`) for compilation and runtime
- **Source Code Clone** (`ccxt-source/`) for IDE navigation and debugging

### ⚠️ CRITICAL: Do NOT modify these configurations without understanding the implications

#### TypeScript Configuration (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "ccxt": ["./node_modules/ccxt", "./ccxt-source/ts/ccxt"],
      "ccxt/*": ["./node_modules/ccxt/*", "./ccxt-source/ts/src/*"]
    }
  },
  "exclude": [
    "ccxt-source",
    "out",
    "dist"
  ]
}
```

**How it works:**
- **Path Resolution Order**: TypeScript tries `node_modules/ccxt` first (for compilation), then falls back to `ccxt-source/ts/ccxt` (for IDE navigation)
- **Compilation**: Uses npm package types and compiled JavaScript
- **IDE Navigation**: Go-to-Definition takes you to actual CCXT source code
- **Exclusions**: Prevents CCXT source from being compiled with your project

#### What This Setup Provides

✅ **Full CCXT API Access**: All error classes (`ccxt.NotSupported`, `ccxt.ExchangeError`, etc.) work normally
✅ **Fast Builds**: Only your project code gets compiled, not CCXT source
✅ **Source Code Navigation**: Ctrl+Click on CCXT symbols opens actual source files
✅ **Debugging**: Can step through CCXT source code during debugging
✅ **Type Safety**: Full TypeScript intellisense and error checking

#### Maintenance Notes

**DO NOT:**
- Remove `baseUrl` or `paths` from `tsconfig.json` (breaks source navigation)
- Add `ccxt-source` to `include` array (will break builds)
- Remove `ccxt-source` from `exclude` array (will cause compilation errors)
- Modify the order of paths in the `paths` mapping (npm package must come first)

**SAFE TO DO:**
- Update CCXT npm package version in `package.json`
- Update CCXT source code in `ccxt-source/` folder
- Add other path mappings to `paths` object

#### Troubleshooting

**If you get `TS2339: Property NotSupported does not exist`:**
- Check that `paths` mapping is intact in `tsconfig.json`
- Verify `ccxt-source/ts/ccxt.ts` exists and exports error classes
- Run `npm install` to ensure npm package is properly installed

**If builds are slow or include CCXT source:**
- Verify `ccxt-source` is in the `exclude` array
- Check that `node_modules/ccxt` comes before `ccxt-source/ts/ccxt` in paths

**If source navigation doesn't work:**
- Ensure `ccxt-source/` folder contains the CCXT source code
- Check that VS Code is using the workspace TypeScript version
- Verify `baseUrl` is set to `"."`

---

## Different exchanges nuances:
- Binance
    - can use both watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols.
    - uses multiple clients (ws-connections) for watchOrderBook and watchOrderBookForSymbols (sometimes mixed and dynamically merged).
    - we use 'exchange-connections-helpers.ts' to close the connections when we are done watching them.
- Bybit
    - uses single client for watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols, however it does have 'unWatchOrderBook' implementation.
    - we use 'monitored-symbols-registry.ts' keep track of monitored symbols and force call "unWatchOrderBook" when we are done watching them.
    - it also doesn't allow to start watching more than 10 symbols in less than 500ms timeframe.
    - we use 'exchange-watch-sheduler.ts' to split the symbols into slots of 10 (or less) and add delays between starting the groups watching.
    - max group size is 10 symbols as well.
- Coinbase
    - uses single client for watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols... at all.
    - only supports 30 symbols per client/connection.
    - we use fetchOrderBook instead of watchOrderBook for it.
    - we use separate exchange instance for each group monitor.
    - when we finish monitoring all symbols, we close the exchange instance of the group monitor.
- Mexc
    - only supports watchOrderBook/unWatchOrderBook methods.
    - uses single client/connection.
    - cannot handle more than 70 symbols per connection.
    - we use individual monitors (several) for it with separate exchange instances.
    - each individual monitor can monitor up to 30 symbols (limited on purpose to improve stability).
    - when we finish monitoring all symbols, we close the exchange instance of the individual monitor.
- Gate
    - only supports watchOrderBook methods.
    - does not support unwatching symbols, however it does have 'unWatchOrderBook' implementation.
    - we use mix of Bybit approach of handling unwatching symbols and Mexc approach of handling multiple individual monitors.